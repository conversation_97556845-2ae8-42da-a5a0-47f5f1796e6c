# Phone Number Search Solution

## Problem
Phone numbers can be stored in the database in three different formats:
1. `919074563560` (raw number)
2. `+919074563560` (with + prefix)
3. `%2B919074563560` (URL encoded + prefix)

When searching for a user by phone number, we need to find matches regardless of which format is used.

## Solution
We created a comprehensive solution that:
1. Normalizes phone numbers to handle all three formats
2. Generates all possible format variations for searching
3. Uses efficient database queries to find matches

## Implementation

### 1. PhoneNumberUtil Class
```typescript
// Location: apps/auth-api/src/utils/phone-number.util.ts
export class PhoneNumberUtil {
  // Cleans phone number to raw format
  static cleanPhoneNumber(phoneNumber: string): string
  
  // Generates all possible formats for searching
  static generatePhoneFormats(phoneNumber: string): string[]
  
  // Converts to specific formats
  static toUrlEncoded(phoneNumber: string): string
  static toPlusFormat(phoneNumber: string): string
  static toRawFormat(phoneNumber: string): string
  
  // Utility methods
  static areEqual(phone1: string, phone2: string): boolean
  static isValid(phoneNumber: string): boolean
}
```

### 2. Updated UserProfileService
```typescript
// Location: apps/auth-api/src/userProfileService/userProfile.service.ts
async find(phoneNumber: string): Promise<UserProfile | undefined> {
  // Generate all possible formats using the utility
  const phoneFormats = PhoneNumberUtil.generatePhoneFormats(phoneNumber);
  
  // Search for any of these formats using the In operator
  const user = await this.userProfileRepository.findOne({
    where: { phone: In(phoneFormats) },
  });
  return user || undefined;
}
```

### 3. Updated Controller
```typescript
// Location: apps/auth-api/src/auth-api.controller.ts
@Post('GenerateOTP')
async generateOTP(@Body() body: GenerateOtpDto): Promise<any> {
  // Find user profile by phone number - handles all format variations
  const userProfile = await this.userProfileService.find(body.phoneNumber);
  
  if (userProfile && userProfile.Type === 'PMS') {
    // Convert to URL encoded format for API call
    const phoneId = PhoneNumberUtil.toUrlEncoded(body.phoneNumber);
    return this.pmsAuthService.sendParentMobileOTPByPass(phoneId);
  }
  // ... rest of the logic
}
```

## Example Usage

### Input Scenarios
```typescript
// All these inputs will find the same user in the database:
const phoneInputs = [
  '919074563560',      // Raw format
  '+919074563560',     // Plus format
  '%2B919074563560'    // URL encoded format
];

// Each input generates these search formats:
PhoneNumberUtil.generatePhoneFormats('919074563560')
// Returns: ['919074563560', '+919074563560', '%2B919074563560']

PhoneNumberUtil.generatePhoneFormats('+919074563560')
// Returns: ['919074563560', '+919074563560', '%2B919074563560']

PhoneNumberUtil.generatePhoneFormats('%2B919074563560')
// Returns: ['919074563560', '+919074563560', '%2B919074563560']
```

### Database Query
```sql
-- The TypeORM query translates to:
SELECT * FROM user_profile 
WHERE phone IN ('919074563560', '+919074563560', '%2B919074563560')
LIMIT 1;
```

## Benefits

1. **Comprehensive Search**: Finds users regardless of how their phone number was stored
2. **Performance**: Uses efficient IN clause instead of multiple OR conditions
3. **Maintainable**: Centralized phone number handling logic
4. **Testable**: Utility functions are easily unit tested
5. **Consistent**: Same logic used across all phone number operations

## Testing
Run the tests to verify the solution:
```bash
npm test -- phone-number.util.spec.ts
```

## Technical Concepts Explained

### TypeORM In Operator
- **In()**: A TypeORM operator that generates SQL IN clause
- **Performance**: More efficient than multiple OR conditions
- **Usage**: `where: { phone: In(['format1', 'format2', 'format3']) }`

### URL Encoding
- **%2B**: URL encoded representation of the + character
- **Why needed**: URLs cannot contain + characters directly
- **Handling**: Our utility automatically handles both %2B and %2b (case insensitive)

### NestJS Service Pattern
- **Injectable**: Services are marked with @Injectable() decorator
- **Dependency Injection**: Services are automatically injected into constructors
- **Separation of Concerns**: Business logic separated from HTTP handling

This solution ensures that phone number searches work reliably regardless of the format used when storing or searching for phone numbers.
